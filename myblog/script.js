// ===== BLOG FUNCTIONALITY =====
// Note: Language switching functionality is handled by inline script in index.html

// Sample blog posts data (in a real implementation, this would come from a CMS or markdown files)
const blogPosts = [
    {
        id: 1,
        title: "Welcome to AI Advances Daily",
        excerpt: "Starting this journey to document and share the incredible advances happening in artificial intelligence every day. From breakthrough research to practical applications, we'll explore it all.",
        date: "2024-12-29",
        readTime: "3 min read",
        featured: true,
        tags: ["Welcome", "AI", "Introduction"]
    },
    {
        id: 2,
        title: "GPT-4 Turbo: OpenAI's Latest Language Model Breakthrough",
        excerpt: "OpenAI has released GPT-4 Turbo, featuring improved reasoning capabilities, longer context windows, and reduced costs. This update represents a significant step forward in large language model development.",
        date: "2024-12-28",
        readTime: "5 min read",
        featured: false,
        tags: ["OpenAI", "GPT-4", "Language Models"]
    },
    {
        id: 3,
        title: "Google's Gemini AI: Multimodal Intelligence Redefined",
        excerpt: "Google's Gemini AI system demonstrates unprecedented multimodal capabilities, seamlessly processing text, images, audio, and video. This advancement marks a new era in AI versatility.",
        date: "2024-12-27",
        readTime: "4 min read",
        featured: false,
        tags: ["Google", "Gemini", "Multimodal AI"]
    },
    {
        id: 4,
        title: "AI in Healthcare: Revolutionary Diagnostic Breakthroughs",
        excerpt: "Recent AI developments in medical imaging and diagnostic tools are revolutionizing healthcare. From early cancer detection to personalized treatment plans, AI is saving lives.",
        date: "2024-12-26",
        readTime: "6 min read",
        featured: false,
        tags: ["Healthcare", "Medical AI", "Diagnostics"]
    },
    {
        id: 5,
        title: "Autonomous Vehicles: Tesla's FSD Beta Progress Report",
        excerpt: "Tesla's Full Self-Driving beta continues to evolve with neural network improvements and real-world testing data. The latest updates show significant progress in complex driving scenarios.",
        date: "2024-12-25",
        readTime: "4 min read",
        featured: false,
        tags: ["Tesla", "Autonomous Vehicles", "FSD"]
    },
    {
        id: 6,
        title: "AI Art Generation: DALL-E 3 vs Midjourney vs Stable Diffusion",
        excerpt: "A comprehensive comparison of the latest AI art generation tools, examining their strengths, limitations, and unique capabilities in creative content generation.",
        date: "2024-12-24",
        readTime: "7 min read",
        featured: false,
        tags: ["AI Art", "DALL-E", "Midjourney", "Stable Diffusion"]
    }
];

// State management
let currentPostsDisplayed = 1;
const postsPerLoad = 3;

// DOM elements
const postsGrid = document.getElementById('postsGrid');
const loadMoreBtn = document.getElementById('loadMoreBtn');
const postCountElement = document.getElementById('postCount');
const mobileMenuBtn = document.getElementById('mobileMenuBtn');
const langButtons = document.querySelectorAll('.lang-btn');

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    initializeBlog();
    setupEventListeners();
    setupScrollAnimations();
});

// ===== MAIN FUNCTIONS =====
function initializeBlog() {
    updatePostCount();
    loadInitialPosts();
    updateLoadMoreButton();
}

function setupEventListeners() {
    // Load more posts
    loadMoreBtn.addEventListener('click', loadMorePosts);

    // Mobile menu toggle
    mobileMenuBtn.addEventListener('click', toggleMobileMenu);

    // Language switcher - handled by inline script

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Update active navigation link on scroll
    window.addEventListener('scroll', updateActiveNavLink);
}

function setupScrollAnimations() {
    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
            }
        });
    }, observerOptions);

    // Observe all post cards and topic cards
    document.querySelectorAll('.post-card, .topic-card').forEach(card => {
        observer.observe(card);
    });
}

// ===== POST MANAGEMENT =====
function loadInitialPosts() {
    postsGrid.innerHTML = '';
    const initialPosts = blogPosts.slice(0, currentPostsDisplayed);
    initialPosts.forEach(post => {
        const postElement = createPostElement(post);
        postsGrid.appendChild(postElement);
    });
}

function loadMorePosts() {
    const startIndex = currentPostsDisplayed;
    const endIndex = Math.min(startIndex + postsPerLoad, blogPosts.length);

    for (let i = startIndex; i < endIndex; i++) {
        const postElement = createPostElement(blogPosts[i]);
        postsGrid.appendChild(postElement);

        // Add animation delay for staggered effect
        setTimeout(() => {
            postElement.classList.add('fade-in-up');
        }, (i - startIndex) * 100);
    }

    currentPostsDisplayed = endIndex;
    updateLoadMoreButton();
}

function createPostElement(post) {
    const postDiv = document.createElement('div');
    postDiv.className = `post-card ${post.featured ? 'featured' : ''}`;

    postDiv.innerHTML = `
        <div class="post-header">
            <span class="post-date">${formatDate(post.date)}</span>
            ${post.featured ? '<span class="post-tag">Featured</span>' : ''}
        </div>
        <h4 class="post-title">${post.title}</h4>
        <p class="post-excerpt">${post.excerpt}</p>
        <div class="post-footer">
            <a href="#" class="read-more" onclick="openPost(${post.id})">Read More</a>
            <div class="post-meta">
                <span class="reading-time">${post.readTime}</span>
            </div>
        </div>
    `;

    return postDiv;
}

function updatePostCount() {
    postCountElement.textContent = blogPosts.length;
}

function updateLoadMoreButton() {
    if (currentPostsDisplayed >= blogPosts.length) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'inline-block';
        const remaining = blogPosts.length - currentPostsDisplayed;
        loadMoreBtn.textContent = `Load More Posts (${remaining} remaining)`;
    }
}

// ===== UTILITY FUNCTIONS =====
function formatDate(dateString) {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
}

function openPost(postId) {
    // In a real implementation, this would navigate to a detailed post page
    const post = blogPosts.find(p => p.id === postId);
    if (post) {
        alert(`Opening post: "${post.title}"\n\nIn a full implementation, this would navigate to a detailed post page with the complete article content.`);
    }
}

// ===== NAVIGATION =====
function toggleMobileMenu() {
    const navLinks = document.querySelector('.nav-links');
    navLinks.classList.toggle('mobile-open');
}

function updateActiveNavLink() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');

    let currentSection = '';

    sections.forEach(section => {
        const sectionTop = section.offsetTop - 100;
        const sectionHeight = section.offsetHeight;

        if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
            currentSection = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${currentSection}`) {
            link.classList.add('active');
        }
    });
}

// ===== SEARCH FUNCTIONALITY (Future Enhancement) =====
function searchPosts(query) {
    // This function can be implemented for search functionality
    const filteredPosts = blogPosts.filter(post =>
        post.title.toLowerCase().includes(query.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(query.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    );

    return filteredPosts;
}

// ===== PERFORMANCE OPTIMIZATIONS =====
// Debounce function for scroll events
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply debouncing to scroll event
window.addEventListener('scroll', debounce(updateActiveNavLink, 100));

// ===== ANALYTICS (Future Enhancement) =====
function trackPageView() {
    // Google Analytics or other analytics tracking
    if (typeof gtag !== 'undefined') {
        gtag('config', 'GA_MEASUREMENT_ID', {
            page_title: document.title,
            page_location: window.location.href
        });
    }
}

function trackPostClick(postId) {
    // Track post engagement
    if (typeof gtag !== 'undefined') {
        gtag('event', 'post_click', {
            'post_id': postId,
            'event_category': 'engagement'
        });
    }
}

// ===== INTERNATIONALIZATION =====
// Language switching functionality is handled by inline script in index.html

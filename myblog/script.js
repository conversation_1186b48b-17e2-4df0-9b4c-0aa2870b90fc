// ===== INTERNATIONALIZATION =====

// Language translations
const translations = {
    en: {
        nav: {
            home: "Home",
            about: "About",
            archive: "Archive",
            contact: "Contact"
        },
        hero: {
            title: "Exploring the Future of AI",
            description: "Daily insights, breakthroughs, and analysis of the rapidly evolving world of artificial intelligence. Stay updated with the latest developments that are shaping our future."
        },
        stats: {
            posts: "Posts",
            updates: "Updates",
            started: "Started"
        },
        posts: {
            title: "Latest Posts",
            subtitle: "Recent insights and discoveries in AI",
            readMore: "Read More",
            loadMore: "Load More Posts",
            remaining: "remaining"
        },
        about: {
            title: "About This Blog",
            description1: "AI Advances Daily is dedicated to tracking and analyzing the rapid developments in artificial intelligence. Every day brings new breakthroughs, research papers, product launches, and insights that shape the future of technology.",
            description2: "This blog serves as a curated collection of the most significant AI advances, explained in an accessible way for both technical and non-technical audiences.",
            topics: {
                ml: {
                    title: "Machine Learning",
                    description: "Latest algorithms, models, and training techniques"
                },
                nlp: {
                    title: "Natural Language",
                    description: "LLMs, chatbots, and language understanding"
                },
                cv: {
                    title: "Computer Vision",
                    description: "Image recognition, generation, and analysis"
                },
                apps: {
                    title: "AI Applications",
                    description: "Real-world implementations and use cases"
                }
            }
        },
        footer: {
            tagline: "Tracking the future of artificial intelligence",
            navigation: "Navigation",
            connect: "Connect",
            copyright: "Built with ❤️ for the AI community."
        }
    },
    ar: {
        nav: {
            home: "الرئيسية",
            about: "حول",
            archive: "الأرشيف",
            contact: "اتصل بنا"
        },
        hero: {
            title: "استكشاف مستقبل الذكاء الاصطناعي",
            description: "رؤى وتحليلات يومية حول التطورات السريعة في عالم الذكاء الاصطناعي. ابق على اطلاع بأحدث التطورات التي تشكل مستقبلنا."
        },
        stats: {
            posts: "المقالات",
            updates: "التحديثات",
            started: "البداية"
        },
        posts: {
            title: "أحدث المقالات",
            subtitle: "رؤى واكتشافات حديثة في الذكاء الاصطناعي",
            readMore: "اقرأ المزيد",
            loadMore: "تحميل المزيد من المقالات",
            remaining: "متبقية"
        },
        about: {
            title: "حول هذه المدونة",
            description1: "تطورات الذكاء الاصطناعي اليومية مخصصة لتتبع وتحليل التطورات السريعة في الذكاء الاصطناعي. كل يوم يجلب اختراقات جديدة وأوراق بحثية وإطلاق منتجات ورؤى تشكل مستقبل التكنولوجيا.",
            description2: "تعمل هذه المدونة كمجموعة منسقة من أهم تطورات الذكاء الاصطناعي، موضحة بطريقة مفهومة للجماهير التقنية وغير التقنية.",
            topics: {
                ml: {
                    title: "التعلم الآلي",
                    description: "أحدث الخوارزميات والنماذج وتقنيات التدريب"
                },
                nlp: {
                    title: "معالجة اللغة الطبيعية",
                    description: "النماذج اللغوية الكبيرة وروبوتات المحادثة وفهم اللغة"
                },
                cv: {
                    title: "الرؤية الحاسوبية",
                    description: "التعرف على الصور وتوليدها وتحليلها"
                },
                apps: {
                    title: "تطبيقات الذكاء الاصطناعي",
                    description: "التطبيقات العملية وحالات الاستخدام"
                }
            }
        },
        footer: {
            tagline: "تتبع مستقبل الذكاء الاصطناعي",
            navigation: "التنقل",
            connect: "تواصل",
            copyright: "مبني بـ ❤️ لمجتمع الذكاء الاصطناعي."
        }
    }
};

// Current language
let currentLanguage = 'en';

// ===== BLOG FUNCTIONALITY =====

// Sample blog posts data (in a real implementation, this would come from a CMS or markdown files)
const blogPosts = [
    {
        id: 1,
        title: "Welcome to AI Advances Daily",
        excerpt: "Starting this journey to document and share the incredible advances happening in artificial intelligence every day. From breakthrough research to practical applications, we'll explore it all.",
        date: "2024-12-29",
        readTime: "3 min read",
        featured: true,
        tags: ["Welcome", "AI", "Introduction"]
    },
    {
        id: 2,
        title: "GPT-4 Turbo: OpenAI's Latest Language Model Breakthrough",
        excerpt: "OpenAI has released GPT-4 Turbo, featuring improved reasoning capabilities, longer context windows, and reduced costs. This update represents a significant step forward in large language model development.",
        date: "2024-12-28",
        readTime: "5 min read",
        featured: false,
        tags: ["OpenAI", "GPT-4", "Language Models"]
    },
    {
        id: 3,
        title: "Google's Gemini AI: Multimodal Intelligence Redefined",
        excerpt: "Google's Gemini AI system demonstrates unprecedented multimodal capabilities, seamlessly processing text, images, audio, and video. This advancement marks a new era in AI versatility.",
        date: "2024-12-27",
        readTime: "4 min read",
        featured: false,
        tags: ["Google", "Gemini", "Multimodal AI"]
    },
    {
        id: 4,
        title: "AI in Healthcare: Revolutionary Diagnostic Breakthroughs",
        excerpt: "Recent AI developments in medical imaging and diagnostic tools are revolutionizing healthcare. From early cancer detection to personalized treatment plans, AI is saving lives.",
        date: "2024-12-26",
        readTime: "6 min read",
        featured: false,
        tags: ["Healthcare", "Medical AI", "Diagnostics"]
    },
    {
        id: 5,
        title: "Autonomous Vehicles: Tesla's FSD Beta Progress Report",
        excerpt: "Tesla's Full Self-Driving beta continues to evolve with neural network improvements and real-world testing data. The latest updates show significant progress in complex driving scenarios.",
        date: "2024-12-25",
        readTime: "4 min read",
        featured: false,
        tags: ["Tesla", "Autonomous Vehicles", "FSD"]
    },
    {
        id: 6,
        title: "AI Art Generation: DALL-E 3 vs Midjourney vs Stable Diffusion",
        excerpt: "A comprehensive comparison of the latest AI art generation tools, examining their strengths, limitations, and unique capabilities in creative content generation.",
        date: "2024-12-24",
        readTime: "7 min read",
        featured: false,
        tags: ["AI Art", "DALL-E", "Midjourney", "Stable Diffusion"]
    }
];

// State management
let currentPostsDisplayed = 1;
const postsPerLoad = 3;

// DOM elements
const postsGrid = document.getElementById('postsGrid');
const loadMoreBtn = document.getElementById('loadMoreBtn');
const postCountElement = document.getElementById('postCount');
const mobileMenuBtn = document.getElementById('mobileMenuBtn');
const langButtons = document.querySelectorAll('.lang-btn');

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    initializeBlog();
    setupEventListeners();
    setupScrollAnimations();
});

// ===== MAIN FUNCTIONS =====
function initializeBlog() {
    updatePostCount();
    loadInitialPosts();
    updateLoadMoreButton();
}

function setupEventListeners() {
    // Load more posts
    loadMoreBtn.addEventListener('click', loadMorePosts);

    // Mobile menu toggle
    mobileMenuBtn.addEventListener('click', toggleMobileMenu);

    // Language switcher
    document.querySelectorAll('.lang-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const lang = this.getAttribute('data-lang');
            switchLanguage(lang);
        });
    });

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Update active navigation link on scroll
    window.addEventListener('scroll', updateActiveNavLink);
}

function setupScrollAnimations() {
    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
            }
        });
    }, observerOptions);

    // Observe all post cards and topic cards
    document.querySelectorAll('.post-card, .topic-card').forEach(card => {
        observer.observe(card);
    });
}

// ===== POST MANAGEMENT =====
function loadInitialPosts() {
    postsGrid.innerHTML = '';
    const initialPosts = blogPosts.slice(0, currentPostsDisplayed);
    initialPosts.forEach(post => {
        const postElement = createPostElement(post);
        postsGrid.appendChild(postElement);
    });
}

function loadMorePosts() {
    const startIndex = currentPostsDisplayed;
    const endIndex = Math.min(startIndex + postsPerLoad, blogPosts.length);

    for (let i = startIndex; i < endIndex; i++) {
        const postElement = createPostElement(blogPosts[i]);
        postsGrid.appendChild(postElement);

        // Add animation delay for staggered effect
        setTimeout(() => {
            postElement.classList.add('fade-in-up');
        }, (i - startIndex) * 100);
    }

    currentPostsDisplayed = endIndex;
    updateLoadMoreButton();
}

function createPostElement(post) {
    const postDiv = document.createElement('div');
    postDiv.className = `post-card ${post.featured ? 'featured' : ''}`;

    postDiv.innerHTML = `
        <div class="post-header">
            <span class="post-date">${formatDate(post.date)}</span>
            ${post.featured ? '<span class="post-tag">Featured</span>' : ''}
        </div>
        <h4 class="post-title">${post.title}</h4>
        <p class="post-excerpt">${post.excerpt}</p>
        <div class="post-footer">
            <a href="#" class="read-more" onclick="openPost(${post.id})">Read More</a>
            <div class="post-meta">
                <span class="reading-time">${post.readTime}</span>
            </div>
        </div>
    `;

    return postDiv;
}

function updatePostCount() {
    postCountElement.textContent = blogPosts.length;
}

function updateLoadMoreButton() {
    const t = translations[currentLanguage];
    if (currentPostsDisplayed >= blogPosts.length) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'inline-block';
        const remaining = blogPosts.length - currentPostsDisplayed;
        loadMoreBtn.textContent = `${t.posts.loadMore} (${remaining} ${t.posts.remaining})`;
    }
}

// ===== UTILITY FUNCTIONS =====
function formatDate(dateString) {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
}

function openPost(postId) {
    // In a real implementation, this would navigate to a detailed post page
    const post = blogPosts.find(p => p.id === postId);
    if (post) {
        alert(`Opening post: "${post.title}"\n\nIn a full implementation, this would navigate to a detailed post page with the complete article content.`);
    }
}

// ===== NAVIGATION =====
function toggleMobileMenu() {
    const navLinks = document.querySelector('.nav-links');
    navLinks.classList.toggle('mobile-open');
}

function updateActiveNavLink() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');

    let currentSection = '';

    sections.forEach(section => {
        const sectionTop = section.offsetTop - 100;
        const sectionHeight = section.offsetHeight;

        if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
            currentSection = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${currentSection}`) {
            link.classList.add('active');
        }
    });
}

// ===== SEARCH FUNCTIONALITY (Future Enhancement) =====
function searchPosts(query) {
    // This function can be implemented for search functionality
    const filteredPosts = blogPosts.filter(post =>
        post.title.toLowerCase().includes(query.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(query.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    );

    return filteredPosts;
}

// ===== PERFORMANCE OPTIMIZATIONS =====
// Debounce function for scroll events
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply debouncing to scroll event
window.addEventListener('scroll', debounce(updateActiveNavLink, 100));

// ===== ANALYTICS (Future Enhancement) =====
function trackPageView() {
    // Google Analytics or other analytics tracking
    if (typeof gtag !== 'undefined') {
        gtag('config', 'GA_MEASUREMENT_ID', {
            page_title: document.title,
            page_location: window.location.href
        });
    }
}

function trackPostClick(postId) {
    // Track post engagement
    if (typeof gtag !== 'undefined') {
        gtag('event', 'post_click', {
            'post_id': postId,
            'event_category': 'engagement'
        });
    }
}

// ===== INTERNATIONALIZATION FUNCTIONS =====
function switchLanguage(lang) {
    if (lang === currentLanguage) return;

    currentLanguage = lang;

    // Update HTML attributes
    document.documentElement.setAttribute('lang', lang);
    document.documentElement.setAttribute('dir', lang === 'ar' ? 'rtl' : 'ltr');

    // Update language buttons
    document.querySelectorAll('.lang-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.getAttribute('data-lang') === lang) {
            btn.classList.add('active');
        }
    });

    // Update all translatable elements
    updateTranslations();

    // Save language preference
    localStorage.setItem('preferred-language', lang);

    // Update page title and meta tags
    updateMetaTags(lang);
}

function updateTranslations() {
    const t = translations[currentLanguage];

    // Update all elements with data-i18n attributes
    document.querySelectorAll('[data-i18n]').forEach(element => {
        const key = element.getAttribute('data-i18n');
        const translation = getNestedTranslation(t, key);
        if (translation) {
            element.textContent = translation;
        }
    });

    // Update specific elements that need special handling
    updateSpecialElements();
}

function getNestedTranslation(obj, path) {
    return path.split('.').reduce((current, key) => current && current[key], obj);
}

function updateSpecialElements() {
    const t = translations[currentLanguage];

    // Update brand subtitle
    const brandSubtitle = document.querySelector('.brand-subtitle');
    if (brandSubtitle) {
        brandSubtitle.textContent = t.footer.tagline;
    }

    // Update stats labels
    const statLabels = document.querySelectorAll('.stat-label');
    if (statLabels.length >= 3) {
        statLabels[0].textContent = t.stats.posts;
        statLabels[1].textContent = t.stats.updates;
        statLabels[2].textContent = t.stats.started;
    }

    // Update section titles and subtitles
    const postsTitle = document.querySelector('.posts-section .section-title');
    const postsSubtitle = document.querySelector('.posts-section .section-subtitle');
    if (postsTitle) postsTitle.textContent = t.posts.title;
    if (postsSubtitle) postsSubtitle.textContent = t.posts.subtitle;

    const aboutTitle = document.querySelector('.about-section .section-title');
    if (aboutTitle) aboutTitle.textContent = t.about.title;

    // Update about content
    const aboutTexts = document.querySelectorAll('.about-text');
    if (aboutTexts.length >= 2) {
        aboutTexts[0].textContent = t.about.description1;
        aboutTexts[1].textContent = t.about.description2;
    }

    // Update topic cards
    const topicCards = document.querySelectorAll('.topic-card');
    const topicKeys = ['ml', 'nlp', 'cv', 'apps'];
    topicCards.forEach((card, index) => {
        if (topicKeys[index]) {
            const title = card.querySelector('.topic-title');
            const description = card.querySelector('.topic-description');
            if (title) title.textContent = t.about.topics[topicKeys[index]].title;
            if (description) description.textContent = t.about.topics[topicKeys[index]].description;
        }
    });

    // Update footer
    const footerBrand = document.querySelector('.footer-brand p');
    if (footerBrand) footerBrand.textContent = t.footer.tagline;

    const footerSections = document.querySelectorAll('.footer-section h5');
    if (footerSections.length >= 2) {
        footerSections[0].textContent = t.footer.navigation;
        footerSections[1].textContent = t.footer.connect;
    }

    const footerCopyright = document.querySelector('.footer-bottom p');
    if (footerCopyright) {
        footerCopyright.innerHTML = `&copy; 2024 AI Advances Daily. ${t.footer.copyright}`;
    }

    // Update load more button
    updateLoadMoreButton();
}

function updateMetaTags(lang) {
    const t = translations[lang];

    // Update title
    document.title = lang === 'ar' ?
        'تطورات الذكاء الاصطناعي اليومية | أحدث أخبار ورؤى الذكاء الاصطناعي' :
        'AI Advances Daily | Latest AI News & Insights';

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
        metaDescription.setAttribute('content',
            lang === 'ar' ?
            'رؤى وتحديثات يومية حول أحدث التطورات في الذكاء الاصطناعي والتعلم الآلي وتكنولوجيا الذكاء الاصطناعي.' :
            'Daily insights and updates on the latest advances in artificial intelligence, machine learning, and AI technology.'
        );
    }
}

function initializeLanguage() {
    // Check for saved language preference or browser language
    const savedLang = localStorage.getItem('preferred-language');
    const browserLang = navigator.language.startsWith('ar') ? 'ar' : 'en';
    const initialLang = savedLang || browserLang;

    if (initialLang !== currentLanguage) {
        switchLanguage(initialLang);
    } else {
        updateTranslations();
    }
}

// Initialize language on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeLanguage();
});

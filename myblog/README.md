# AI Advances Daily 🤖

A modern, clean, and minimal static blog focused on daily insights and updates about artificial intelligence advances.

## 🎨 Design Features

- **Clean & Minimal**: Modern design with focus on readability
- **Color Scheme**: Dark gray, green, and white theme
- **Responsive**: Mobile-first design that works on all devices
- **Fast Loading**: Static site optimized for performance
- **SEO Optimized**: Meta tags and semantic HTML structure

## 🚀 Technologies Used

- **HTML5**: Semantic markup for better accessibility
- **CSS3**: Modern CSS with CSS Grid and Flexbox
- **Vanilla JavaScript**: No frameworks, pure performance
- **Google Fonts**: Inter font family for clean typography
- **GitHub Pages**: Free hosting and deployment

## 📁 Project Structure

```
myblog/
├── index.html          # Main HTML file
├── styles.css          # All CSS styles
├── script.js           # JavaScript functionality
├── README.md           # This file
└── posts/              # Future: Individual blog posts
    └── (markdown files)
```

## 🛠️ Setup & Deployment

### Local Development

1. **Clone the repository**:
   ```bash
   git clone https://github.com/rwadalebsar/myblog.git
   cd myblog
   ```

2. **Open locally**:
   - Simply open `index.html` in your browser
   - Or use a local server:
     ```bash
     python -m http.server 8000
     # Then visit http://localhost:8000
     ```

### GitHub Pages Deployment

1. **Push to GitHub**:
   ```bash
   git add .
   git commit -m "Initial blog setup"
   git push origin main
   ```

2. **Enable GitHub Pages**:
   - Go to your repository settings
   - Scroll to "Pages" section
   - Select "Deploy from a branch"
   - Choose "main" branch and "/ (root)" folder
   - Save

3. **Access your blog**:
   - Your blog will be available at: `https://rwadalebsar.github.io/myblog/`

## ✍️ Adding New Posts

Currently, posts are stored in the `script.js` file in the `blogPosts` array. To add a new post:

1. **Edit `script.js`**:
   ```javascript
   const blogPosts = [
       {
           id: 7, // Increment the ID
           title: "Your New Post Title",
           excerpt: "Brief description of your post...",
           date: "2024-12-30", // YYYY-MM-DD format
           readTime: "5 min read",
           featured: false, // Set to true for featured posts
           tags: ["AI", "Technology", "News"]
       },
       // ... existing posts
   ];
   ```

2. **Commit and push**:
   ```bash
   git add script.js
   git commit -m "Add new post: Your Post Title"
   git push origin main
   ```

## 🎯 Content Guidelines

### Post Structure
- **Title**: Clear, descriptive, SEO-friendly
- **Excerpt**: 2-3 sentences summarizing the post
- **Date**: Use YYYY-MM-DD format
- **Read Time**: Estimate based on ~200 words per minute
- **Tags**: 2-4 relevant tags for categorization

### Content Focus
- Daily AI advances and breakthroughs
- Research paper summaries
- Product launches and updates
- Industry analysis and insights
- Practical AI applications

## 🔧 Customization

### Colors
The color scheme is defined in CSS variables at the top of `styles.css`:
```css
:root {
  --color-primary: #10b981;        /* Green */
  --color-dark: #1f2937;           /* Dark gray */
  --color-white: #ffffff;          /* White */
  /* ... more colors */
}
```

### Typography
- Font: Inter (Google Fonts)
- Sizes defined in CSS variables
- Responsive scaling

### Layout
- Max width: 1200px
- Grid-based responsive design
- Mobile-first approach

## 📈 Future Enhancements

### Phase 1 (Immediate)
- [ ] Add individual post pages
- [ ] Implement search functionality
- [ ] Add RSS feed
- [ ] Include social sharing buttons

### Phase 2 (Medium-term)
- [ ] Markdown-based posts
- [ ] Comment system (Disqus/GitHub Issues)
- [ ] Newsletter signup
- [ ] Analytics integration

### Phase 3 (Long-term)
- [ ] CMS integration (Netlify CMS/Forestry)
- [ ] Dark/light mode toggle
- [ ] Advanced filtering and categorization
- [ ] Performance optimizations

## 📊 SEO & Analytics

### Built-in SEO Features
- Semantic HTML structure
- Meta tags for social sharing
- Proper heading hierarchy
- Alt tags for images (when added)
- Fast loading times

### Analytics Setup
To add Google Analytics:
1. Get your GA4 measurement ID
2. Add the tracking code to `index.html`
3. Uncomment analytics functions in `script.js`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test locally
5. Submit a pull request

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🔗 Links

- **Live Site**: https://rwadalebsar.github.io/myblog/
- **Repository**: https://github.com/rwadalebsar/myblog
- **Issues**: https://github.com/rwadalebsar/myblog/issues

---

Built with ❤️ for the AI community. Happy blogging! 🚀

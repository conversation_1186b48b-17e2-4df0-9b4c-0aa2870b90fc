// ===== INTERNATIONALIZATION SYSTEM =====
console.log('🌍 i18n.js file loaded successfully!');

// Language translations
const translations = {
    en: {
        nav: {
            home: "Home",
            about: "About",
            archive: "Archive",
            contact: "Contact"
        },
        hero: {
            title: "Exploring the Future of AI",
            description: "Daily insights, breakthroughs, and analysis of the rapidly evolving world of artificial intelligence. Stay updated with the latest developments that are shaping our future."
        },
        posts: {
            title: "Latest Posts",
            subtitle: "Recent insights and discoveries in AI",
            readMore: "Read More",
            loadMore: "Load More Posts"
        },
        about: {
            title: "About This Blog",
            description1: "AI Advances Daily is dedicated to tracking and analyzing the rapid developments in artificial intelligence.",
            description2: "This blog serves as a curated collection of the most significant AI advances, explained in an accessible way."
        }
    },
    ar: {
        nav: {
            home: "الرئيسية",
            about: "حول",
            archive: "الأرشيف",
            contact: "اتصل بنا"
        },
        hero: {
            title: "استكشاف مستقبل الذكاء الاصطناعي",
            description: "رؤى وتحليلات يومية حول التطورات السريعة في عالم الذكاء الاصطناعي. ابق على اطلاع بأحدث التطورات التي تشكل مستقبلنا."
        },
        posts: {
            title: "أحدث المقالات",
            subtitle: "رؤى واكتشافات حديثة في الذكاء الاصطناعي",
            readMore: "اقرأ المزيد",
            loadMore: "تحميل المزيد من المقالات"
        },
        about: {
            title: "حول هذه المدونة",
            description1: "تطورات الذكاء الاصطناعي اليومية مخصصة لتتبع وتحليل التطورات السريعة في الذكاء الاصطناعي.",
            description2: "تعمل هذه المدونة كمجموعة منسقة من أهم تطورات الذكاء الاصطناعي، موضحة بطريقة مفهومة."
        }
    }
};

// Current language
let currentLanguage = 'en';

// Language switching function
function switchLanguage(lang) {
    if (lang === currentLanguage) return;

    currentLanguage = lang;

    // Update HTML attributes
    document.documentElement.setAttribute('lang', lang);
    document.documentElement.setAttribute('dir', lang === 'ar' ? 'rtl' : 'ltr');

    // Update language buttons
    document.querySelectorAll('.lang-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.getAttribute('data-lang') === lang) {
            btn.classList.add('active');
        }
    });

    // Update translations
    updateTranslations();

    // Save preference
    localStorage.setItem('preferred-language', lang);

    // Update page title
    document.title = lang === 'ar' ?
        'تطورات الذكاء الاصطناعي اليومية | أحدث أخبار ورؤى الذكاء الاصطناعي' :
        'AI Advances Daily | Latest AI News & Insights';
}

function updateTranslations() {
    const t = translations[currentLanguage];

    // Update navigation
    const navLinks = document.querySelectorAll('.nav-link');
    const navKeys = ['home', 'about', 'archive', 'contact'];
    navLinks.forEach((link, index) => {
        if (navKeys[index] && t.nav[navKeys[index]]) {
            link.textContent = t.nav[navKeys[index]];
        }
    });

    // Update hero section
    const heroTitle = document.querySelector('.hero-title');
    const heroDescription = document.querySelector('.hero-description');
    if (heroTitle) heroTitle.textContent = t.hero.title;
    if (heroDescription) heroDescription.textContent = t.hero.description;

    // Update posts section
    const postsTitle = document.querySelector('.posts-section .section-title');
    const postsSubtitle = document.querySelector('.posts-section .section-subtitle');
    if (postsTitle) postsTitle.textContent = t.posts.title;
    if (postsSubtitle) postsSubtitle.textContent = t.posts.subtitle;

    // Update about section
    const aboutTitle = document.querySelector('.about-section .section-title');
    if (aboutTitle) aboutTitle.textContent = t.about.title;

    const aboutTexts = document.querySelectorAll('.about-text');
    if (aboutTexts.length >= 2) {
        aboutTexts[0].textContent = t.about.description1;
        aboutTexts[1].textContent = t.about.description2;
    }

    // Update read more links
    document.querySelectorAll('.read-more').forEach(link => {
        link.textContent = t.posts.readMore;
    });

    // Update load more button
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        loadMoreBtn.textContent = t.posts.loadMore;
    }
}

// Initialize language system
function initializeLanguage() {
    // Check for saved language preference
    const savedLang = localStorage.getItem('preferred-language');
    const browserLang = navigator.language.startsWith('ar') ? 'ar' : 'en';
    const initialLang = savedLang || browserLang;

    if (initialLang !== currentLanguage) {
        switchLanguage(initialLang);
    }
}

// Setup language switcher event listeners
function setupLanguageSwitcher() {
    const langButtons = document.querySelectorAll('.lang-btn');
    console.log('i18n.js: Found', langButtons.length, 'language buttons');

    langButtons.forEach((btn, index) => {
        console.log('i18n.js: Setting up button', index, 'with data-lang:', btn.getAttribute('data-lang'));
        btn.addEventListener('click', function() {
            const lang = this.getAttribute('data-lang');
            console.log('i18n.js: Language button clicked:', lang);
            switchLanguage(lang);
        });
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('i18n.js: DOMContentLoaded fired');
    setupLanguageSwitcher();
    initializeLanguage();
});

// Also try to initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        console.log('i18n.js: DOMContentLoaded fired (backup)');
        setupLanguageSwitcher();
        initializeLanguage();
    });
} else {
    console.log('i18n.js: DOM already loaded, initializing immediately');
    setupLanguageSwitcher();
    initializeLanguage();
}

<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language Switcher Test</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="rtl.css">
    <style>
        body { padding: 20px; font-family: Arial, sans-serif; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .language-switcher { margin: 20px 0; }
    </style>
</head>
<body>
    <h1>Language Switcher Test</h1>
    
    <!-- Language Switcher -->
    <div class="language-switcher">
        <button class="lang-btn active" data-lang="en">EN</button>
        <button class="lang-btn" data-lang="ar">عربي</button>
    </div>
    
    <div class="test-section">
        <h2 class="hero-title">Exploring the Future of AI</h2>
        <p class="hero-description">Daily insights, breakthroughs, and analysis of the rapidly evolving world of artificial intelligence.</p>
    </div>
    
    <div class="test-section">
        <h3 class="section-title">Latest Posts</h3>
        <p class="section-subtitle">Recent insights and discoveries in AI</p>
    </div>

    <script>
        console.log('🔧 Test page loaded');
        
        // Language translations
        const translations = {
            en: {
                title: "Exploring the Future of AI",
                description: "Daily insights, breakthroughs, and analysis of the rapidly evolving world of artificial intelligence.",
                posts: "Latest Posts",
                subtitle: "Recent insights and discoveries in AI"
            },
            ar: {
                title: "استكشاف مستقبل الذكاء الاصطناعي",
                description: "رؤى وتحليلات يومية حول التطورات السريعة في عالم الذكاء الاصطناعي.",
                posts: "أحدث المقالات",
                subtitle: "رؤى واكتشافات حديثة في الذكاء الاصطناعي"
            }
        };
        
        // Simple language switcher
        function setupLanguageSwitcher() {
            const langButtons = document.querySelectorAll('.lang-btn');
            console.log('Found buttons:', langButtons.length);
            
            langButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const lang = this.getAttribute('data-lang');
                    console.log('Button clicked:', lang);
                    
                    // Update active state
                    langButtons.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    
                    // Update HTML attributes
                    document.documentElement.setAttribute('lang', lang);
                    document.documentElement.setAttribute('dir', lang === 'ar' ? 'rtl' : 'ltr');
                    
                    // Update text content
                    const t = translations[lang];
                    document.querySelector('.hero-title').textContent = t.title;
                    document.querySelector('.hero-description').textContent = t.description;
                    document.querySelector('.section-title').textContent = t.posts;
                    document.querySelector('.section-subtitle').textContent = t.subtitle;
                    
                    console.log('Language switched to:', lang);
                });
            });
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, setting up language switcher');
            setupLanguageSwitcher();
        });
    </script>
</body>
</html>

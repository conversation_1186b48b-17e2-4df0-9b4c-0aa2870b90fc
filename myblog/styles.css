/* ===== CSS VARIABLES ===== */
:root {
  /* Colors - Dark Gray, Green, White theme */
  --color-primary: #10b981;        /* Green */
  --color-primary-dark: #059669;   /* Darker green */
  --color-primary-light: #34d399;  /* Lighter green */

  --color-dark: #1f2937;           /* Dark gray */
  --color-dark-light: #374151;     /* Medium dark gray */
  --color-gray: #6b7280;           /* Gray */
  --color-gray-light: #9ca3af;     /* Light gray */

  --color-white: #ffffff;          /* Pure white */
  --color-off-white: #f9fafb;      /* Off white */
  --color-light: #f3f4f6;          /* Very light gray */

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 0.75rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Layout */
  --max-width: 1200px;
  --border-radius: 0.5rem;
  --border-radius-lg: 0.75rem;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Transitions */
  --transition: all 0.3s ease;
}

/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--color-dark);
  background-color: var(--color-white);
  overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin-bottom: var(--spacing-md);
  color: var(--color-gray);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--color-primary-dark);
}

/* ===== LAYOUT COMPONENTS ===== */
.container {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* ===== HEADER ===== */
.header {
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-light);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.nav {
  padding: var(--spacing-lg) 0;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.nav-brand {
  flex: 1;
}

.brand-title {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--color-dark);
  margin-bottom: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.brand-icon {
  font-size: var(--font-size-3xl);
}

.brand-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-gray);
  margin-bottom: 0;
}

.nav-links {
  display: flex;
  gap: var(--spacing-xl);
  align-items: center;
}

.nav-link {
  font-weight: 500;
  color: var(--color-gray);
  transition: var(--transition);
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: var(--color-primary);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--color-primary);
  border-radius: 1px;
}

.mobile-menu-btn {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-sm);
}

.mobile-menu-btn span {
  width: 24px;
  height: 2px;
  background-color: var(--color-dark);
  transition: var(--transition);
}

/* ===== HERO SECTION ===== */
.hero {
  background: linear-gradient(135deg, var(--color-off-white) 0%, var(--color-light) 100%);
  padding: var(--spacing-3xl) 0;
  text-align: center;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--color-dark);
  margin-bottom: var(--spacing-lg);
}

.hero-description {
  font-size: var(--font-size-lg);
  color: var(--color-gray);
  margin-bottom: var(--spacing-2xl);
  line-height: 1.7;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-2xl);
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-gray);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* ===== SECTIONS ===== */
.posts-section,
.about-section {
  padding: var(--spacing-3xl) 0;
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.section-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--color-dark);
  margin-bottom: var(--spacing-md);
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-gray);
  margin-bottom: 0;
}

/* ===== POSTS GRID ===== */
.posts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.post-card {
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow);
  transition: var(--transition);
  border: 1px solid var(--color-light);
}

.post-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.post-card.featured {
  border-left: 4px solid var(--color-primary);
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.post-date {
  font-size: var(--font-size-sm);
  color: var(--color-gray);
}

.post-tag {
  background-color: var(--color-primary);
  color: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.post-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-dark);
  margin-bottom: var(--spacing-md);
  line-height: 1.4;
}

.post-excerpt {
  color: var(--color-gray);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
}

.post-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.read-more {
  color: var(--color-primary);
  font-weight: 500;
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.post-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.reading-time {
  font-size: var(--font-size-sm);
  color: var(--color-gray-light);
}

/* ===== LOAD MORE BUTTON ===== */
.load-more-container {
  text-align: center;
}

.load-more-btn {
  background-color: var(--color-primary);
  color: var(--color-white);
  border: none;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  font-size: var(--font-size-base);
}

.load-more-btn:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-2px);
}

/* ===== ABOUT SECTION ===== */
.about-section {
  background-color: var(--color-off-white);
}

.about-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.about-text {
  font-size: var(--font-size-lg);
  line-height: 1.7;
  margin-bottom: var(--spacing-lg);
}

.topics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-2xl);
}

.topic-card {
  background-color: var(--color-white);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  text-align: center;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.topic-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.topic-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-md);
}

.topic-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-dark);
  margin-bottom: var(--spacing-sm);
}

.topic-description {
  color: var(--color-gray);
  font-size: var(--font-size-sm);
  margin-bottom: 0;
}

/* ===== FOOTER ===== */
.footer {
  background-color: var(--color-dark);
  color: var(--color-white);
  padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-xl);
}

.footer-brand h4 {
  color: var(--color-white);
  margin-bottom: var(--spacing-sm);
}

.footer-brand p {
  color: var(--color-gray-light);
  margin-bottom: 0;
}

.footer-links {
  display: flex;
  gap: var(--spacing-2xl);
}

.footer-section h5 {
  color: var(--color-white);
  font-size: var(--font-size-sm);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--spacing-md);
}

.footer-section a {
  display: block;
  color: var(--color-gray-light);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-sm);
  transition: var(--transition);
}

.footer-section a:hover {
  color: var(--color-primary-light);
}

.footer-bottom {
  border-top: 1px solid var(--color-dark-light);
  padding-top: var(--spacing-lg);
  text-align: center;
}

.footer-bottom p {
  color: var(--color-gray-light);
  font-size: var(--font-size-sm);
  margin-bottom: 0;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .mobile-menu-btn {
    display: flex;
  }

  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .hero-stats {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .posts-grid {
    grid-template-columns: 1fr;
  }

  .topics-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .footer-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-md);
  }

  .nav-container {
    padding: 0 var(--spacing-md);
  }

  .brand-title {
    font-size: var(--font-size-xl);
  }

  .hero {
    padding: var(--spacing-2xl) 0;
  }

  .posts-section,
  .about-section {
    padding: var(--spacing-2xl) 0;
  }
}

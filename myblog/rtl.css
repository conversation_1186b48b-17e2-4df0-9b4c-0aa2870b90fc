/* ===== RTL SUPPORT & LANGUAGE SWITCHER ===== */

/* Language Switcher Styles */
.language-switcher {
  display: flex;
  gap: 0.25rem;
  margin-left: 1.5rem;
  border: 1px solid #f3f4f6;
  border-radius: 0.5rem;
  overflow: hidden;
}

.lang-btn {
  background: none;
  border: none;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 40px;
}

.lang-btn:hover {
  background-color: #f3f4f6;
  color: #1f2937;
}

.lang-btn.active {
  background-color: #10b981;
  color: #ffffff;
}

/* Arabic Font Support */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

/* RTL Layout Support */
[dir="rtl"] {
  font-family: 'Noto Sans Arabic', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

[dir="rtl"] .nav-container {
  direction: rtl;
}

[dir="rtl"] .nav-links {
  flex-direction: row-reverse;
}

[dir="rtl"] .language-switcher {
  margin-left: 0;
  margin-right: 1.5rem;
}

[dir="rtl"] .hero-stats {
  direction: rtl;
}

[dir="rtl"] .posts-grid {
  direction: rtl;
}

[dir="rtl"] .post-header {
  flex-direction: row-reverse;
}

[dir="rtl"] .post-footer {
  flex-direction: row-reverse;
}

[dir="rtl"] .footer-content {
  direction: rtl;
}

[dir="rtl"] .footer-links {
  flex-direction: row-reverse;
}

/* RTL Text Alignment */
[dir="rtl"] .hero-title,
[dir="rtl"] .section-title,
[dir="rtl"] .post-title {
  text-align: right;
}

[dir="rtl"] .hero-description,
[dir="rtl"] .section-subtitle,
[dir="rtl"] .post-excerpt,
[dir="rtl"] .about-text {
  text-align: right;
}

/* RTL Responsive Design */
@media (max-width: 768px) {
  [dir="rtl"] .nav-links {
    flex-direction: column;
    text-align: right;
  }
  
  [dir="rtl"] .language-switcher {
    margin-right: 0;
    justify-content: center;
  }
}

/* Smooth transitions for language switching */
.hero-title,
.hero-description,
.section-title,
.section-subtitle,
.post-title,
.post-excerpt,
.about-text,
.nav-link {
  transition: all 0.3s ease;
}

/* Enhanced Arabic typography */
[dir="rtl"] .brand-title,
[dir="rtl"] .hero-title,
[dir="rtl"] .section-title {
  font-family: 'Noto Sans Arabic', sans-serif;
  font-weight: 600;
  line-height: 1.4;
}

[dir="rtl"] .post-title {
  font-family: 'Noto Sans Arabic', sans-serif;
  font-weight: 500;
  line-height: 1.5;
}

/* RTL-specific spacing adjustments */
[dir="rtl"] .post-card {
  text-align: right;
}

[dir="rtl"] .topic-card {
  text-align: right;
}

[dir="rtl"] .stat {
  text-align: center; /* Keep stats centered in both directions */
}

#!/usr/bin/env python3
"""
MQTT Tank Level Simulator
Simulates tank level readings sent to an MQTT broker
"""

import paho.mqtt.client as mqtt
import json
import time
import random
import threading
from datetime import datetime
import argparse

class TankSimulator:
    def __init__(self, broker_host="localhost", broker_port=1883, topic_prefix="tanks"):
        self.broker_host = broker_host
        self.broker_port = broker_port
        self.topic_prefix = topic_prefix
        self.client = None
        self.running = False
        
        # Tank configurations
        self.tanks = {
            "tank1": {"name": "Main Storage Tank", "capacity": 1000, "level": 75.5},
            "tank2": {"name": "Secondary Tank", "capacity": 500, "level": 45.2},
            "tank3": {"name": "Emergency Reserve", "capacity": 200, "level": 89.1}
        }
        
    def on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            print(f"✅ Connected to MQTT broker at {self.broker_host}:{self.broker_port}")
            print(f"📡 Publishing to topic prefix: {self.topic_prefix}")
        else:
            print(f"❌ Failed to connect to MQTT broker. Return code: {rc}")
    
    def on_disconnect(self, client, userdata, rc):
        print(f"🔌 Disconnected from MQTT broker")
    
    def on_publish(self, client, userdata, mid):
        print(f"📤 Message published (mid: {mid})")
    
    def start_simulator(self):
        """Start the MQTT simulator"""
        try:
            # Create MQTT client
            self.client = mqtt.Client(client_id=f"tank_simulator_{random.randint(1000, 9999)}")
            
            # Set callbacks
            self.client.on_connect = self.on_connect
            self.client.on_disconnect = self.on_disconnect
            self.client.on_publish = self.on_publish
            
            # Connect to broker
            print(f"🔄 Connecting to MQTT broker at {self.broker_host}:{self.broker_port}...")
            self.client.connect(self.broker_host, self.broker_port, 60)
            
            # Start the network loop
            self.client.loop_start()
            
            self.running = True
            print(f"🚀 MQTT Tank Simulator started!")
            print(f"📊 Simulating {len(self.tanks)} tanks")
            
            # Start publishing data
            self.publish_loop()
            
        except Exception as e:
            print(f"❌ Error starting MQTT simulator: {e}")
    
    def publish_loop(self):
        """Main loop to publish tank data"""
        while self.running:
            try:
                for tank_id, tank_data in self.tanks.items():
                    # Simulate level changes
                    change = random.uniform(-2.0, 2.0)
                    tank_data["level"] = max(0, min(100, tank_data["level"] + change))
                    
                    # Create message
                    message = {
                        "tank_id": tank_id,
                        "name": tank_data["name"],
                        "level": round(tank_data["level"], 2),
                        "capacity": tank_data["capacity"],
                        "status": self.get_tank_status(tank_data["level"]),
                        "timestamp": datetime.now().isoformat(),
                        "unit": "percentage"
                    }
                    
                    # Publish to topic
                    topic = f"{self.topic_prefix}/{tank_id}/level"
                    payload = json.dumps(message)
                    
                    result = self.client.publish(topic, payload, qos=1)
                    
                    if result.rc == mqtt.MQTT_ERR_SUCCESS:
                        print(f"📡 Published {tank_id}: {tank_data['level']:.1f}% to {topic}")
                    else:
                        print(f"❌ Failed to publish {tank_id}: {result.rc}")
                
                # Wait before next update
                time.sleep(30)  # Publish every 30 seconds
                
            except KeyboardInterrupt:
                print("\n🛑 Stopping MQTT simulator...")
                self.stop_simulator()
                break
            except Exception as e:
                print(f"❌ Error in publish loop: {e}")
                time.sleep(5)
    
    def get_tank_status(self, level):
        """Determine tank status based on level"""
        if level < 20:
            return "LOW"
        elif level > 90:
            return "HIGH"
        else:
            return "NORMAL"
    
    def stop_simulator(self):
        """Stop the MQTT simulator"""
        self.running = False
        if self.client:
            self.client.loop_stop()
            self.client.disconnect()
        print("🔌 MQTT simulator stopped")

def main():
    parser = argparse.ArgumentParser(description="MQTT Tank Level Simulator")
    parser.add_argument("--broker", default="localhost", help="MQTT broker host")
    parser.add_argument("--port", type=int, default=1883, help="MQTT broker port")
    parser.add_argument("--topic-prefix", default="tanks", help="Topic prefix for messages")
    
    args = parser.parse_args()
    
    # Create and start simulator
    simulator = TankSimulator(
        broker_host=args.broker,
        broker_port=args.port,
        topic_prefix=args.topic_prefix
    )
    
    try:
        simulator.start_simulator()
    except KeyboardInterrupt:
        print("\n🛑 Simulator interrupted by user")
        simulator.stop_simulator()

if __name__ == "__main__":
    main()
